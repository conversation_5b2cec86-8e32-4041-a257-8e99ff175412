package com.gt06.network.client;

import com.gt06.protocol.message.AlarmMessage;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * GT06设备模拟器
 * 模拟真实GPS设备的完整生命周期和行为
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@Slf4j
public class DeviceSimulator {
    
    private final GT06TestClient client;
    private final String deviceId;
    
    // 模拟器状态
    private final AtomicBoolean running = new AtomicBoolean(false);
    private final AtomicBoolean loggedIn = new AtomicBoolean(false);
    private ScheduledExecutorService scheduler;
    
    // 模拟器配置
    private int heartbeatIntervalSeconds = 30;
    private int locationReportIntervalSeconds = 60;
    private double currentLatitude = 39.9042; // 北京天安门
    private double currentLongitude = 116.4074;
    private int currentSpeed = 0;
    
    // 统计信息
    private final AtomicInteger totalMessagesSent = new AtomicInteger(0);
    private final AtomicInteger heartbeatsSent = new AtomicInteger(0);
    private final AtomicInteger locationsSent = new AtomicInteger(0);
    private final AtomicInteger alarmsSent = new AtomicInteger(0);
    
    /**
     * 构造函数
     * 
     * @param host 服务器地址
     * @param port 服务器端口
     * @param deviceId 设备ID
     */
    public DeviceSimulator(String host, int port, String deviceId) {
        this.deviceId = deviceId;
        this.client = new GT06TestClient(host, port, deviceId);
        log.info("🤖 DeviceSimulator created for device: {}", deviceId);
    }
    
    /**
     * 启动设备模拟器
     * 
     * @return 启动是否成功
     */
    public boolean start() {
        if (running.get()) {
            log.warn("⚠️ Device simulator is already running");
            return true;
        }
        
        log.info("🚀 Starting device simulator for: {}", deviceId);
        
        try {
            // 连接到服务器
            if (!client.connect()) {
                log.error("❌ Failed to connect to server");
                return false;
            }
            
            // 发送登录消息
            if (!client.sendLogin()) {
                log.error("❌ Failed to login");
                client.disconnect();
                return false;
            }
            
            loggedIn.set(true);
            running.set(true);
            
            // 启动定时任务
            startScheduledTasks();
            
            log.info("✅ Device simulator started successfully for: {}", deviceId);
            return true;
            
        } catch (Exception e) {
            log.error("❌ Failed to start device simulator: {}", e.getMessage(), e);
            stop();
            return false;
        }
    }
    
    /**
     * 停止设备模拟器
     */
    public void stop() {
        log.info("🛑 Stopping device simulator for: {}", deviceId);
        
        running.set(false);
        loggedIn.set(false);
        
        // 停止定时任务
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        // 断开连接
        client.disconnect();
        
        log.info("✅ Device simulator stopped for: {}", deviceId);
        printStatistics();
    }
    
    /**
     * 启动定时任务
     */
    private void startScheduledTasks() {
        scheduler = Executors.newScheduledThreadPool(3);
        
        // 心跳任务
        scheduler.scheduleAtFixedRate(this::sendHeartbeat, 
            heartbeatIntervalSeconds, heartbeatIntervalSeconds, TimeUnit.SECONDS);
        
        // 位置上报任务
        scheduler.scheduleAtFixedRate(this::sendLocationReport, 
            locationReportIntervalSeconds, locationReportIntervalSeconds, TimeUnit.SECONDS);
        
        // 随机事件任务（报警等）
        scheduler.scheduleAtFixedRate(this::triggerRandomEvent, 
            120, 180, TimeUnit.SECONDS); // 每2-3分钟触发一次随机事件
        
        log.info("📅 Scheduled tasks started - heartbeat: {}s, location: {}s", 
            heartbeatIntervalSeconds, locationReportIntervalSeconds);
    }
    
    /**
     * 发送心跳
     */
    private void sendHeartbeat() {
        if (!running.get() || !client.isConnected()) {
            return;
        }
        
        try {
            if (client.sendHeartbeat()) {
                heartbeatsSent.incrementAndGet();
                totalMessagesSent.incrementAndGet();
                log.debug("💓 Heartbeat sent successfully");
            } else {
                log.warn("⚠️ Failed to send heartbeat");
            }
        } catch (Exception e) {
            log.error("❌ Error sending heartbeat: {}", e.getMessage());
        }
    }
    
    /**
     * 发送位置报告
     */
    private void sendLocationReport() {
        if (!running.get() || !client.isConnected()) {
            return;
        }
        
        try {
            // 模拟位置变化
            simulateMovement();
            
            if (client.sendLocation(currentLatitude, currentLongitude, currentSpeed)) {
                locationsSent.incrementAndGet();
                totalMessagesSent.incrementAndGet();
                log.debug("📍 Location report sent: lat={}, lng={}, speed={}km/h", 
                    currentLatitude, currentLongitude, currentSpeed);
            } else {
                log.warn("⚠️ Failed to send location report");
            }
        } catch (Exception e) {
            log.error("❌ Error sending location report: {}", e.getMessage());
        }
    }
    
    /**
     * 模拟设备移动
     */
    private void simulateMovement() {
        // 简单的随机移动模拟
        double latChange = (Math.random() - 0.5) * 0.001; // 约100米范围
        double lngChange = (Math.random() - 0.5) * 0.001;
        
        currentLatitude += latChange;
        currentLongitude += lngChange;
        
        // 模拟速度变化
        if (Math.random() < 0.3) { // 30%概率改变速度
            currentSpeed = (int)(Math.random() * 80); // 0-80km/h
        }
    }
    
    /**
     * 触发随机事件
     */
    private void triggerRandomEvent() {
        if (!running.get() || !client.isConnected()) {
            return;
        }
        
        try {
            double eventProbability = Math.random();
            
            if (eventProbability < 0.1) { // 10%概率发送报警
                sendRandomAlarm();
            } else if (eventProbability < 0.2) { // 10%概率模拟指令响应
                simulateCommandResponse();
            }
            // 80%概率不做任何事情
            
        } catch (Exception e) {
            log.error("❌ Error in random event: {}", e.getMessage());
        }
    }
    
    /**
     * 发送随机报警
     */
    private void sendRandomAlarm() {
        AlarmMessage.AlarmType[] alarmTypes = {
            AlarmMessage.AlarmType.VIBRATION,
            AlarmMessage.AlarmType.POWER_OFF,
            AlarmMessage.AlarmType.OVERSPEED,
            AlarmMessage.AlarmType.TAMPER
        };
        
        AlarmMessage.AlarmType randomAlarm = alarmTypes[(int)(Math.random() * alarmTypes.length)];
        
        if (client.sendAlarm(randomAlarm)) {
            alarmsSent.incrementAndGet();
            totalMessagesSent.incrementAndGet();
            log.info("🚨 Random alarm sent: {}", randomAlarm.getDescription());
        } else {
            log.warn("⚠️ Failed to send random alarm");
        }
    }
    
    /**
     * 模拟指令响应
     */
    private void simulateCommandResponse() {
        // 这里可以模拟接收到服务器指令后的响应
        log.debug("📡 Simulating command response handling");
    }
    
    /**
     * 模拟紧急情况
     * 
     * @param alarmType 报警类型
     */
    public void simulateEmergency(AlarmMessage.AlarmType alarmType) {
        if (!running.get() || !client.isConnected()) {
            log.warn("⚠️ Cannot simulate emergency: device not running or connected");
            return;
        }
        
        log.warn("🚨 Simulating emergency: {}", alarmType.getDescription());
        
        if (client.sendAlarm(alarmType)) {
            alarmsSent.incrementAndGet();
            totalMessagesSent.incrementAndGet();
            log.info("✅ Emergency alarm sent successfully");
        } else {
            log.error("❌ Failed to send emergency alarm");
        }
    }
    
    /**
     * 模拟长距离行驶
     * 
     * @param targetLat 目标纬度
     * @param targetLng 目标经度
     * @param durationMinutes 行驶时间（分钟）
     */
    public void simulateTrip(double targetLat, double targetLng, int durationMinutes) {
        if (!running.get()) {
            log.warn("⚠️ Cannot simulate trip: device not running");
            return;
        }
        
        log.info("🚗 Starting trip simulation to ({}, {}) over {} minutes", 
            targetLat, targetLng, durationMinutes);
        
        double startLat = currentLatitude;
        double startLng = currentLongitude;
        
        // 创建行程任务
        ScheduledExecutorService tripScheduler = Executors.newSingleThreadScheduledExecutor();
        AtomicInteger stepCount = new AtomicInteger(0);
        int totalSteps = durationMinutes; // 每分钟一个位置点
        
        tripScheduler.scheduleAtFixedRate(() -> {
            int step = stepCount.incrementAndGet();
            double progress = (double) step / totalSteps;
            
            if (progress >= 1.0) {
                currentLatitude = targetLat;
                currentLongitude = targetLng;
                currentSpeed = 0;
                tripScheduler.shutdown();
                log.info("🏁 Trip simulation completed");
                return;
            }
            
            // 线性插值计算当前位置
            currentLatitude = startLat + (targetLat - startLat) * progress;
            currentLongitude = startLng + (targetLng - startLng) * progress;
            currentSpeed = 60 + (int)(Math.random() * 40); // 60-100km/h
            
            // 发送位置更新
            if (client.isConnected()) {
                client.sendLocation(currentLatitude, currentLongitude, currentSpeed);
                log.debug("🚗 Trip progress: {:.1f}% - lat={}, lng={}", 
                    progress * 100, currentLatitude, currentLongitude);
            }
            
        }, 0, 1, TimeUnit.MINUTES);
    }
    
    /**
     * 打印统计信息
     */
    public void printStatistics() {
        log.info("📊 Device Simulator Statistics for {}:", deviceId);
        log.info("  - Total messages sent: {}", totalMessagesSent.get());
        log.info("  - Heartbeats sent: {}", heartbeatsSent.get());
        log.info("  - Locations sent: {}", locationsSent.get());
        log.info("  - Alarms sent: {}", alarmsSent.get());
        log.info("  - Current position: ({}, {})", currentLatitude, currentLongitude);
        log.info("  - Current speed: {}km/h", currentSpeed);
    }
    
    // Getter和Setter方法
    public boolean isRunning() { return running.get(); }
    public boolean isLoggedIn() { return loggedIn.get(); }
    public String getDeviceId() { return deviceId; }
    public GT06TestClient getClient() { return client; }
    
    public void setHeartbeatInterval(int seconds) { this.heartbeatIntervalSeconds = seconds; }
    public void setLocationReportInterval(int seconds) { this.locationReportIntervalSeconds = seconds; }
    public void setCurrentPosition(double latitude, double longitude) {
        this.currentLatitude = latitude;
        this.currentLongitude = longitude;
    }
    public void setCurrentSpeed(int speed) { this.currentSpeed = speed; }
    
    public int getTotalMessagesSent() { return totalMessagesSent.get(); }
    public int getHeartbeatsSent() { return heartbeatsSent.get(); }
    public int getLocationsSent() { return locationsSent.get(); }
    public int getAlarmsSent() { return alarmsSent.get(); }
}
