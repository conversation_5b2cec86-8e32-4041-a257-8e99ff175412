package com.gt06.network.client;

import com.gt06.common.constants.GT06Constants;
import com.gt06.common.util.ByteUtil;
import com.gt06.protocol.message.GT06Message;
import com.gt06.protocol.message.MessageFactory;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.ByteToMessageDecoder;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * GT06客户端消息解码器
 * 专门用于客户端接收服务器响应消息的解码
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@Slf4j
public class GT06ClientMessageDecoder extends ByteToMessageDecoder {
    
    private long totalMessages = 0;
    private long successfulMessages = 0;
    private long failedMessages = 0;
    
    @Override
    protected void decode(ChannelHandlerContext ctx, ByteBuf in, List<Object> out) throws Exception {
        try {
            // 查找完整的GT06消息
            int messageLength = findCompleteMessage(in);
            if (messageLength == -1) {
                return; // 没有完整消息，等待更多数据
            }
            
            // 读取完整消息到新的ByteBuf
            ByteBuf messageBuf = in.readRetainedSlice(messageLength);
            
            try {
                // 解析消息
                GT06Message message = MessageFactory.parseMessage(messageBuf);
                
                // 设置消息的接收时间
                message.setReceivedTime(java.time.LocalDateTime.now());
                
                // 添加到输出列表
                out.add(message);
                
                totalMessages++;
                successfulMessages++;
                
                log.debug("📥 Client decoded message: {} from {}", 
                    message.getMessageDescription(), 
                    ctx.channel().remoteAddress());
                
            } catch (Exception e) {
                log.error("❌ Client failed to parse GT06 message from {}: {}", 
                    ctx.channel().remoteAddress(), e.getMessage());
                
                if (log.isDebugEnabled()) {
                    log.debug("Failed message bytes: {}", 
                        ByteUtil.bytesToHexWithSpace(messageBuf.array()));
                }
                
                failedMessages++;
                
                // 不抛出异常，继续处理后续消息
            } finally {
                messageBuf.release();
            }
            
        } catch (Exception e) {
            log.error("❌ Unexpected error in GT06 client message decoder", e);
            in.resetReaderIndex();
            failedMessages++;
        }
    }
    
    /**
     * 查找完整的GT06消息
     * 
     * @param buffer 输入缓冲区
     * @return 完整消息的长度，如果没有找到返回-1
     */
    private int findCompleteMessage(ByteBuf buffer) {
        int readerIndex = buffer.readerIndex();
        int readableBytes = buffer.readableBytes();
        
        // 查找起始位
        for (int i = 0; i < readableBytes - 1; i++) {
            if (buffer.getByte(readerIndex + i) == GT06Constants.START_BITS[0] &&
                buffer.getByte(readerIndex + i + 1) == GT06Constants.START_BITS[1]) {
                
                // 找到起始位，检查是否有完整消息
                int startPos = readerIndex + i;
                
                // 检查是否有足够字节读取包长度
                if (startPos + 3 >= buffer.writerIndex()) {
                    return -1; // 数据不够
                }
                
                // 读取包长度
                int packetLength = buffer.getUnsignedByte(startPos + 2);
                
                // 计算完整消息长度
                int totalLength = 2 + 1 + packetLength + 2; // 起始位 + 长度 + 数据 + 停止位
                
                // 检查是否有完整消息
                if (startPos + totalLength <= buffer.writerIndex()) {
                    // 验证停止位
                    int stopPos = startPos + 3 + packetLength;
                    if (buffer.getByte(stopPos) == GT06Constants.STOP_BITS[0] &&
                        buffer.getByte(stopPos + 1) == GT06Constants.STOP_BITS[1]) {
                        
                        // 如果起始位不在当前读取位置，需要跳过前面的无效字节
                        if (i > 0) {
                            buffer.skipBytes(i);
                        }
                        
                        return totalLength;
                    }
                }
            }
        }
        
        // 没有找到完整消息
        return -1;
    }
    
    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        log.error("❌ Exception in GT06ClientMessageDecoder for channel {}", 
            ctx.channel().remoteAddress(), cause);
        
        // 关闭连接
        ctx.close();
    }
    
    /**
     * 获取解码统计信息
     * 
     * @return 解码统计
     */
    public DecoderStats getStats() {
        return new DecoderStats(totalMessages, successfulMessages, failedMessages);
    }
    
    /**
     * 重置统计信息
     */
    public void resetStats() {
        totalMessages = 0;
        successfulMessages = 0;
        failedMessages = 0;
    }
    
    /**
     * 解码统计信息
     */
    public static class DecoderStats {
        private final long totalMessages;
        private final long successfulMessages;
        private final long failedMessages;
        
        public DecoderStats(long totalMessages, long successfulMessages, long failedMessages) {
            this.totalMessages = totalMessages;
            this.successfulMessages = successfulMessages;
            this.failedMessages = failedMessages;
        }
        
        public long getTotalMessages() { return totalMessages; }
        public long getSuccessfulMessages() { return successfulMessages; }
        public long getFailedMessages() { return failedMessages; }
        public double getSuccessRate() { 
            return totalMessages > 0 ? (double) successfulMessages / totalMessages : 0.0; 
        }
        
        @Override
        public String toString() {
            return String.format("DecoderStats{total=%d, success=%d, failed=%d, rate=%.2f%%}", 
                totalMessages, successfulMessages, failedMessages, getSuccessRate() * 100);
        }
    }
}
