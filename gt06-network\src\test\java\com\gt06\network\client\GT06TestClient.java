package com.gt06.network.client;

import com.gt06.common.constants.GT06Constants;
import com.gt06.protocol.message.*;
import io.netty.bootstrap.Bootstrap;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.handler.timeout.IdleStateHandler;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * GT06协议测试客户端
 * 模拟GPS设备与服务器的通信
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@Slf4j
public class GT06TestClient {
    
    private final String host;
    private final int port;
    private final String deviceImei;
    
    private EventLoopGroup workerGroup;
    private Channel channel;
    private volatile boolean connected = false;
    private final AtomicInteger serialNumber = new AtomicInteger(1);
    
    // 响应等待机制
    private volatile GT06Message lastResponse;
    private final Object responseLock = new Object();
    
    /**
     * 构造函数
     * 
     * @param host 服务器地址
     * @param port 服务器端口
     * @param deviceImei 设备IMEI号
     */
    public GT06TestClient(String host, int port, String deviceImei) {
        this.host = host;
        this.port = port;
        this.deviceImei = deviceImei;
        log.info("🔧 GT06TestClient created for device: {} -> {}:{}", deviceImei, host, port);
    }
    
    /**
     * 连接到服务器
     * 
     * @return 连接是否成功
     */
    public boolean connect() {
        return connect(5000); // 默认5秒超时
    }
    
    /**
     * 连接到服务器
     * 
     * @param timeoutMs 连接超时时间（毫秒）
     * @return 连接是否成功
     */
    public boolean connect(long timeoutMs) {
        if (connected) {
            log.warn("⚠️ Client is already connected");
            return true;
        }
        
        log.info("🔌 Connecting to GT06 server {}:{}...", host, port);
        
        try {
            workerGroup = new NioEventLoopGroup(1);
            
            Bootstrap bootstrap = new Bootstrap();
            bootstrap.group(workerGroup)
                    .channel(NioSocketChannel.class)
                    .option(ChannelOption.SO_KEEPALIVE, true)
                    .option(ChannelOption.TCP_NODELAY, true)
                    .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, (int) timeoutMs)
                    .handler(new ChannelInitializer<SocketChannel>() {
                        @Override
                        protected void initChannel(SocketChannel ch) throws Exception {
                            ChannelPipeline pipeline = ch.pipeline();
                            
                            // 空闲状态检测
                            pipeline.addLast("idleStateHandler", new IdleStateHandler(
                                    300, 60, 360, TimeUnit.SECONDS));
                            
                            // GT06协议编解码器
                            pipeline.addLast("gt06Decoder", new GT06ClientMessageDecoder());
                            pipeline.addLast("gt06Encoder", new GT06ClientMessageEncoder());
                            
                            // 客户端消息处理器
                            pipeline.addLast("clientHandler", new GT06ClientHandler());
                        }
                    });
            
            // 连接服务器
            ChannelFuture future = bootstrap.connect(host, port);
            channel = future.sync().channel();
            
            connected = true;
            log.info("✅ Successfully connected to GT06 server {}:{}", host, port);
            log.info("📡 Local address: {}", channel.localAddress());
            log.info("📡 Remote address: {}", channel.remoteAddress());
            
            return true;
            
        } catch (Exception e) {
            log.error("❌ Failed to connect to GT06 server {}:{}: {}", host, port, e.getMessage());
            disconnect();
            return false;
        }
    }
    
    /**
     * 断开连接
     */
    public void disconnect() {
        log.info("🔌 Disconnecting from GT06 server...");
        
        connected = false;
        
        if (channel != null && channel.isActive()) {
            try {
                channel.close().sync();
                log.info("✅ Channel closed successfully");
            } catch (InterruptedException e) {
                log.warn("⚠️ Interrupted while closing channel", e);
                Thread.currentThread().interrupt();
            }
        }
        
        if (workerGroup != null) {
            workerGroup.shutdownGracefully(2, 10, TimeUnit.SECONDS);
            log.info("✅ Worker group shutdown completed");
        }
        
        log.info("🛑 GT06TestClient disconnected");
    }
    
    /**
     * 发送登录消息
     * 
     * @return 发送是否成功
     */
    public boolean sendLogin() {
        if (!isConnected()) {
            log.error("❌ Cannot send login: client not connected");
            return false;
        }
        
        log.info("📤 Sending login message for device: {}", deviceImei);
        
        try {
            LoginMessage loginMessage = new LoginMessage(deviceImei);
            loginMessage.setSerialNumber(getNextSerialNumber());
            
            return sendMessageAndWaitResponse(loginMessage, 5000);
            
        } catch (Exception e) {
            log.error("❌ Failed to send login message: {}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 发送心跳消息
     * 
     * @return 发送是否成功
     */
    public boolean sendHeartbeat() {
        if (!isConnected()) {
            log.error("❌ Cannot send heartbeat: client not connected");
            return false;
        }
        
        log.debug("💓 Sending heartbeat message");
        
        try {
            HeartbeatMessage heartbeat = new HeartbeatMessage();
            heartbeat.setSerialNumber(getNextSerialNumber());
            heartbeat.setVoltageLevel(4); // 模拟电压等级
            heartbeat.setGsmSignalStrength(25); // 模拟信号强度
            heartbeat.setCharging(true);
            heartbeat.setAccOn(true);
            
            return sendMessageAndWaitResponse(heartbeat, 3000);
            
        } catch (Exception e) {
            log.error("❌ Failed to send heartbeat: {}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 发送位置数据
     * 
     * @param latitude 纬度
     * @param longitude 经度
     * @param speed 速度
     * @return 发送是否成功
     */
    public boolean sendLocation(double latitude, double longitude, int speed) {
        if (!isConnected()) {
            log.error("❌ Cannot send location: client not connected");
            return false;
        }
        
        log.info("📍 Sending location: lat={}, lng={}, speed={}km/h", latitude, longitude, speed);
        
        try {
            LocationMessage location = new LocationMessage();
            location.setSerialNumber(getNextSerialNumber());
            location.setGpsDateTime(LocalDateTime.now());
            location.setLatitude(latitude);
            location.setLongitude(longitude);
            location.setSpeed(speed);
            location.setDirection((int)(Math.random() * 360)); // 随机方向
            location.setGpsFixed(true);
            location.setAccOn(true);
            location.setSatelliteCount(8);
            
            // 模拟LBS信息
            location.setMcc(460); // 中国移动
            location.setMnc(0);
            location.setLac(12345);
            location.setCellId(67890);
            location.setSignalStrength(20);
            
            return sendMessageAndWaitResponse(location, 5000);
            
        } catch (Exception e) {
            log.error("❌ Failed to send location: {}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 发送报警消息
     * 
     * @param alarmType 报警类型
     * @return 发送是否成功
     */
    public boolean sendAlarm(AlarmMessage.AlarmType alarmType) {
        if (!isConnected()) {
            log.error("❌ Cannot send alarm: client not connected");
            return false;
        }
        
        log.warn("🚨 Sending alarm: {}", alarmType.getDescription());
        
        try {
            AlarmMessage alarm = new AlarmMessage();
            alarm.setSerialNumber(getNextSerialNumber());
            alarm.setAlarmDateTime(LocalDateTime.now());
            alarm.setAlarmType(alarmType);
            alarm.setAlarmLevel(AlarmMessage.AlarmLevel.HIGH);
            
            // 添加位置信息
            alarm.setLatitude(39.9042);
            alarm.setLongitude(116.4074);
            alarm.setGpsFixed(true);
            alarm.setAccOn(true);
            
            return sendMessageAndWaitResponse(alarm, 5000);
            
        } catch (Exception e) {
            log.error("❌ Failed to send alarm: {}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 检查连接状态
     * 
     * @return 是否已连接
     */
    public boolean isConnected() {
        return connected && channel != null && channel.isActive();
    }
    
    /**
     * 获取下一个序列号
     * 
     * @return 序列号
     */
    private int getNextSerialNumber() {
        return serialNumber.getAndIncrement() & 0xFFFF; // 保持在16位范围内
    }
    
    /**
     * 发送消息并等待响应
     * 
     * @param message 要发送的消息
     * @param timeoutMs 超时时间
     * @return 是否成功
     */
    private boolean sendMessageAndWaitResponse(GT06Message message, long timeoutMs) {
        if (channel == null || !channel.isActive()) {
            return false;
        }
        
        try {
            // 清除之前的响应
            synchronized (responseLock) {
                lastResponse = null;
            }
            
            // 发送消息
            ChannelFuture future = channel.writeAndFlush(message);
            if (!future.await(timeoutMs, TimeUnit.MILLISECONDS)) {
                log.error("❌ Send message timeout: {}", message.getMessageDescription());
                return false;
            }
            
            if (!future.isSuccess()) {
                log.error("❌ Failed to send message: {}", future.cause().getMessage());
                return false;
            }
            
            // 等待响应
            synchronized (responseLock) {
                if (lastResponse == null) {
                    responseLock.wait(timeoutMs);
                }
                
                if (lastResponse != null) {
                    log.debug("✅ Received response: {}", lastResponse.getMessageDescription());
                    return true;
                } else {
                    log.warn("⚠️ No response received within {}ms", timeoutMs);
                    return false;
                }
            }
            
        } catch (Exception e) {
            log.error("❌ Error sending message: {}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 客户端消息处理器
     */
    private class GT06ClientHandler extends SimpleChannelInboundHandler<GT06Message> {
        
        @Override
        protected void channelRead0(ChannelHandlerContext ctx, GT06Message msg) throws Exception {
            log.debug("📥 Received message: {}", msg.getMessageDescription());
            
            // 通知等待的线程
            synchronized (responseLock) {
                lastResponse = msg;
                responseLock.notifyAll();
            }
        }
        
        @Override
        public void channelInactive(ChannelHandlerContext ctx) throws Exception {
            log.warn("🔌 Channel became inactive: {}", ctx.channel().remoteAddress());
            connected = false;
            super.channelInactive(ctx);
        }
        
        @Override
        public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
            log.error("❌ Exception in client handler: {}", cause.getMessage(), cause);
            ctx.close();
        }
    }
    
    // Getter methods
    public String getHost() { return host; }
    public int getPort() { return port; }
    public String getDeviceImei() { return deviceImei; }
    public int getCurrentSerialNumber() { return serialNumber.get(); }
}
