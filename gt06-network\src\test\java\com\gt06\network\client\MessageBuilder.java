package com.gt06.network.client;

import com.gt06.protocol.message.*;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.Random;

/**
 * GT06消息构建工具类
 * 提供快速创建各种类型测试消息的静态方法
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@Slf4j
public class MessageBuilder {
    
    private static final Random random = new Random();
    
    // 默认测试位置（北京天安门）
    private static final double DEFAULT_LATITUDE = 39.9042;
    private static final double DEFAULT_LONGITUDE = 116.4074;
    
    // 私有构造函数，防止实例化
    private MessageBuilder() {}
    
    /**
     * 创建登录消息
     * 
     * @param imei 设备IMEI号
     * @param serialNumber 序列号
     * @return 登录消息
     */
    public static LoginMessage createLoginMessage(String imei, int serialNumber) {
        LoginMessage login = new LoginMessage(imei);
        login.setSerialNumber(serialNumber);
        
        log.debug("📝 Created login message for device: {}", imei);
        return login;
    }
    
    /**
     * 创建心跳消息
     * 
     * @param serialNumber 序列号
     * @return 心跳消息
     */
    public static HeartbeatMessage createHeartbeatMessage(int serialNumber) {
        HeartbeatMessage heartbeat = new HeartbeatMessage();
        heartbeat.setSerialNumber(serialNumber);
        
        // 模拟设备状态
        heartbeat.setVoltageLevel(random.nextInt(6) + 1); // 1-6级电压
        heartbeat.setGsmSignalStrength(random.nextInt(31) + 1); // 1-31信号强度
        heartbeat.setCharging(random.nextBoolean());
        heartbeat.setAccOn(random.nextBoolean());
        heartbeat.setGpsTracking(true);
        heartbeat.setArmed(random.nextBoolean());
        
        log.debug("💓 Created heartbeat message with voltage={}, signal={}", 
            heartbeat.getVoltageLevel(), heartbeat.getGsmSignalStrength());
        return heartbeat;
    }
    
    /**
     * 创建位置消息（使用默认位置）
     * 
     * @param serialNumber 序列号
     * @return 位置消息
     */
    public static LocationMessage createLocationMessage(int serialNumber) {
        return createLocationMessage(serialNumber, DEFAULT_LATITUDE, DEFAULT_LONGITUDE, 
            random.nextInt(120)); // 随机速度0-120km/h
    }
    
    /**
     * 创建位置消息
     * 
     * @param serialNumber 序列号
     * @param latitude 纬度
     * @param longitude 经度
     * @param speed 速度
     * @return 位置消息
     */
    public static LocationMessage createLocationMessage(int serialNumber, double latitude, 
                                                       double longitude, int speed) {
        LocationMessage location = new LocationMessage();
        location.setSerialNumber(serialNumber);
        location.setGpsDateTime(LocalDateTime.now());
        
        // GPS信息
        location.setLatitude(latitude);
        location.setLongitude(longitude);
        location.setSpeed(speed);
        location.setDirection(random.nextInt(360)); // 随机方向
        location.setAltitude(random.nextInt(1000) + 50); // 50-1050米海拔
        location.setGpsFixed(true);
        location.setAccOn(random.nextBoolean());
        location.setSatelliteCount(random.nextInt(8) + 4); // 4-12颗卫星
        location.setDifferentialGps(random.nextBoolean());
        
        // LBS信息（模拟中国移动网络）
        location.setMcc(460); // 中国
        location.setMnc(random.nextInt(3)); // 0=移动, 1=联通, 2=电信
        location.setLac(random.nextInt(65535));
        location.setCellId(random.nextInt(65535));
        location.setSignalStrength(random.nextInt(31) + 1);
        
        // 可选里程信息
        if (random.nextBoolean()) {
            location.setMileage((long)(random.nextInt(100000) + 1000)); // 1-100km
        }
        
        location.setLocationType(LocationMessage.LocationType.GPS_LBS);
        
        log.debug("📍 Created location message: lat={}, lng={}, speed={}km/h", 
            latitude, longitude, speed);
        return location;
    }
    
    /**
     * 创建报警消息
     * 
     * @param serialNumber 序列号
     * @param alarmType 报警类型
     * @return 报警消息
     */
    public static AlarmMessage createAlarmMessage(int serialNumber, AlarmMessage.AlarmType alarmType) {
        return createAlarmMessage(serialNumber, alarmType, DEFAULT_LATITUDE, DEFAULT_LONGITUDE);
    }
    
    /**
     * 创建报警消息
     * 
     * @param serialNumber 序列号
     * @param alarmType 报警类型
     * @param latitude 纬度
     * @param longitude 经度
     * @return 报警消息
     */
    public static AlarmMessage createAlarmMessage(int serialNumber, AlarmMessage.AlarmType alarmType,
                                                 double latitude, double longitude) {
        AlarmMessage alarm = new AlarmMessage();
        alarm.setSerialNumber(serialNumber);
        alarm.setAlarmDateTime(LocalDateTime.now());
        alarm.setAlarmType(alarmType);
        
        // 根据报警类型设置级别
        switch (alarmType) {
            case SOS:
            case COLLISION:
                alarm.setAlarmLevel(AlarmMessage.AlarmLevel.CRITICAL);
                break;
            case VIBRATION:
            case OVERSPEED:
            case TAMPER:
                alarm.setAlarmLevel(AlarmMessage.AlarmLevel.HIGH);
                break;
            case FENCE_IN:
            case FENCE_OUT:
            case RAPID_ACCELERATION:
            case RAPID_DECELERATION:
            case SHARP_TURN:
                alarm.setAlarmLevel(AlarmMessage.AlarmLevel.MEDIUM);
                break;
            default:
                alarm.setAlarmLevel(AlarmMessage.AlarmLevel.LOW);
        }
        
        // 位置信息
        alarm.setLatitude(latitude);
        alarm.setLongitude(longitude);
        alarm.setSpeed(random.nextInt(120));
        alarm.setDirection(random.nextInt(360));
        alarm.setGpsFixed(true);
        alarm.setAccOn(random.nextBoolean());
        
        // LBS信息
        alarm.setMcc(460);
        alarm.setMnc(random.nextInt(3));
        alarm.setLac(random.nextInt(65535));
        alarm.setCellId(random.nextInt(65535));
        alarm.setSignalStrength(random.nextInt(31) + 1);
        
        log.warn("🚨 Created alarm message: {} [{}]", 
            alarmType.getDescription(), alarm.getAlarmLevel().getDescription());
        return alarm;
    }
    
    /**
     * 创建指令消息
     * 
     * @param serialNumber 序列号
     * @param commandType 指令类型
     * @return 指令消息
     */
    public static CommandMessage createCommandMessage(int serialNumber, CommandMessage.CommandType commandType) {
        // 使用CommandMessage的构造函数
        CommandMessage command = new CommandMessage(commandType);
        command.setSerialNumber(serialNumber);

        log.info("📤 Created command message: {}", commandType.getDescription());
        return command;
    }
    
    /**
     * 创建随机位置消息（在指定范围内）
     * 
     * @param serialNumber 序列号
     * @param centerLat 中心纬度
     * @param centerLng 中心经度
     * @param radiusKm 半径（公里）
     * @return 位置消息
     */
    public static LocationMessage createRandomLocationMessage(int serialNumber, double centerLat, 
                                                             double centerLng, double radiusKm) {
        // 在指定半径内生成随机位置
        double angle = random.nextDouble() * 2 * Math.PI;
        double distance = random.nextDouble() * radiusKm;
        
        // 简单的坐标偏移计算（适用于小范围）
        double latOffset = distance * Math.cos(angle) / 111.0; // 1度纬度约111km
        double lngOffset = distance * Math.sin(angle) / (111.0 * Math.cos(Math.toRadians(centerLat)));
        
        double newLat = centerLat + latOffset;
        double newLng = centerLng + lngOffset;
        
        return createLocationMessage(serialNumber, newLat, newLng, random.nextInt(120));
    }
    
    /**
     * 创建模拟行驶轨迹的位置消息
     * 
     * @param serialNumber 序列号
     * @param startLat 起始纬度
     * @param startLng 起始经度
     * @param endLat 结束纬度
     * @param endLng 结束经度
     * @param progress 进度（0.0-1.0）
     * @return 位置消息
     */
    public static LocationMessage createTrajectoryLocationMessage(int serialNumber, 
                                                                 double startLat, double startLng,
                                                                 double endLat, double endLng,
                                                                 double progress) {
        // 线性插值计算当前位置
        double currentLat = startLat + (endLat - startLat) * progress;
        double currentLng = startLng + (endLng - startLng) * progress;
        
        // 根据进度调整速度（模拟加速减速）
        int speed;
        if (progress < 0.1 || progress > 0.9) {
            speed = random.nextInt(30) + 10; // 起步和停车阶段：10-40km/h
        } else {
            speed = random.nextInt(60) + 40; // 正常行驶：40-100km/h
        }
        
        return createLocationMessage(serialNumber, currentLat, currentLng, speed);
    }
    
    /**
     * 创建常见报警类型的快捷方法
     */
    public static AlarmMessage createSOSAlarm(int serialNumber) {
        return createAlarmMessage(serialNumber, AlarmMessage.AlarmType.SOS);
    }
    
    public static AlarmMessage createOverspeedAlarm(int serialNumber) {
        return createAlarmMessage(serialNumber, AlarmMessage.AlarmType.OVERSPEED);
    }
    
    public static AlarmMessage createPowerOffAlarm(int serialNumber) {
        return createAlarmMessage(serialNumber, AlarmMessage.AlarmType.POWER_OFF);
    }

    public static AlarmMessage createTamperAlarm(int serialNumber) {
        return createAlarmMessage(serialNumber, AlarmMessage.AlarmType.TAMPER);
    }

    public static AlarmMessage createCollisionAlarm(int serialNumber) {
        return createAlarmMessage(serialNumber, AlarmMessage.AlarmType.COLLISION);
    }

    public static AlarmMessage createFenceInAlarm(int serialNumber) {
        return createAlarmMessage(serialNumber, AlarmMessage.AlarmType.FENCE_IN);
    }

    public static AlarmMessage createFenceOutAlarm(int serialNumber) {
        return createAlarmMessage(serialNumber, AlarmMessage.AlarmType.FENCE_OUT);
    }
    
    public static AlarmMessage createVibrationAlarm(int serialNumber) {
        return createAlarmMessage(serialNumber, AlarmMessage.AlarmType.VIBRATION);
    }
}
