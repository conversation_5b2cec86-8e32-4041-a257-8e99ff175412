package com.gt06.network.integration;

import com.gt06.network.client.DeviceSimulator;
import com.gt06.network.client.GT06TestClient;
import com.gt06.network.client.MessageBuilder;
import com.gt06.network.server.GT06Server;
import com.gt06.protocol.message.AlarmMessage;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * GT06服务器集成测试
 * 测试服务器与客户端的完整交互流程
 * 
 * <AUTHOR> System
 * @version 1.0.0
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class GT06ServerIntegrationTest {
    
    private static final String TEST_HOST = "localhost";
    private static final int TEST_PORT = 8888;
    private static final String TEST_DEVICE_IMEI = "123456789012345";
    
    private GT06TestClient testClient;
    private DeviceSimulator deviceSimulator;
    
    @BeforeEach
    void setUp() {
        log.info("🔧 Setting up integration test...");
        testClient = new GT06TestClient(TEST_HOST, TEST_PORT, TEST_DEVICE_IMEI);
        deviceSimulator = new DeviceSimulator(TEST_HOST, TEST_PORT, TEST_DEVICE_IMEI);
    }
    
    @AfterEach
    void tearDown() {
        log.info("🧹 Cleaning up integration test...");
        
        if (deviceSimulator != null && deviceSimulator.isRunning()) {
            deviceSimulator.stop();
        }
        
        if (testClient != null && testClient.isConnected()) {
            testClient.disconnect();
        }
        
        // 等待连接完全关闭
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    @Test
    @Order(1)
    @DisplayName("测试基本连接和登录")
    void testBasicConnectionAndLogin() {
        log.info("🧪 Testing basic connection and login...");
        
        // 测试连接
        boolean connected = testClient.connect(10000);
        Assertions.assertTrue(connected, "Should connect to server successfully");
        Assertions.assertTrue(testClient.isConnected(), "Client should be connected");
        
        // 测试登录
        boolean loginSuccess = testClient.sendLogin();
        Assertions.assertTrue(loginSuccess, "Login should be successful");
        
        log.info("✅ Basic connection and login test passed");
    }
    
    @Test
    @Order(2)
    @DisplayName("测试心跳消息")
    void testHeartbeatMessage() {
        log.info("🧪 Testing heartbeat message...");
        
        // 连接并登录
        Assertions.assertTrue(testClient.connect(10000));
        Assertions.assertTrue(testClient.sendLogin());
        
        // 发送心跳
        boolean heartbeatSuccess = testClient.sendHeartbeat();
        Assertions.assertTrue(heartbeatSuccess, "Heartbeat should be sent successfully");
        
        log.info("✅ Heartbeat message test passed");
    }
    
    @Test
    @Order(3)
    @DisplayName("测试位置数据上报")
    void testLocationReporting() {
        log.info("🧪 Testing location reporting...");
        
        // 连接并登录
        Assertions.assertTrue(testClient.connect(10000));
        Assertions.assertTrue(testClient.sendLogin());
        
        // 发送位置数据
        double testLat = 39.9042; // 北京天安门
        double testLng = 116.4074;
        int testSpeed = 60;
        
        boolean locationSuccess = testClient.sendLocation(testLat, testLng, testSpeed);
        Assertions.assertTrue(locationSuccess, "Location should be sent successfully");
        
        log.info("✅ Location reporting test passed");
    }
    
    @Test
    @Order(4)
    @DisplayName("测试报警消息")
    void testAlarmMessages() {
        log.info("🧪 Testing alarm messages...");
        
        // 连接并登录
        Assertions.assertTrue(testClient.connect(10000));
        Assertions.assertTrue(testClient.sendLogin());
        
        // 测试不同类型的报警
        AlarmMessage.AlarmType[] alarmTypes = {
            AlarmMessage.AlarmType.SOS,
            AlarmMessage.AlarmType.OVERSPEED,
            AlarmMessage.AlarmType.LOW_BATTERY,
            AlarmMessage.AlarmType.VIBRATION
        };
        
        for (AlarmMessage.AlarmType alarmType : alarmTypes) {
            boolean alarmSuccess = testClient.sendAlarm(alarmType);
            Assertions.assertTrue(alarmSuccess, 
                "Alarm " + alarmType.getDescription() + " should be sent successfully");
            
            // 短暂延迟避免消息过于密集
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        
        log.info("✅ Alarm messages test passed");
    }
    
    @Test
    @Order(5)
    @DisplayName("测试设备模拟器")
    void testDeviceSimulator() {
        log.info("🧪 Testing device simulator...");
        
        // 启动设备模拟器
        boolean started = deviceSimulator.start();
        Assertions.assertTrue(started, "Device simulator should start successfully");
        Assertions.assertTrue(deviceSimulator.isRunning(), "Device simulator should be running");
        Assertions.assertTrue(deviceSimulator.isLoggedIn(), "Device should be logged in");
        
        // 运行一段时间观察自动消息发送
        try {
            Thread.sleep(5000); // 运行5秒
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 检查统计信息
        Assertions.assertTrue(deviceSimulator.getTotalMessagesSent() > 0, 
            "Should have sent some messages automatically");
        
        // 模拟紧急情况
        deviceSimulator.simulateEmergency(AlarmMessage.AlarmType.SOS);
        
        // 停止模拟器
        deviceSimulator.stop();
        Assertions.assertFalse(deviceSimulator.isRunning(), "Device simulator should be stopped");
        
        log.info("✅ Device simulator test passed");
    }
    
    @Test
    @Order(6)
    @DisplayName("测试并发连接")
    void testConcurrentConnections() {
        log.info("🧪 Testing concurrent connections...");
        
        int concurrentClients = 5;
        List<GT06TestClient> clients = new ArrayList<>();
        ExecutorService executor = Executors.newFixedThreadPool(concurrentClients);
        CountDownLatch latch = new CountDownLatch(concurrentClients);
        
        try {
            // 创建多个并发客户端
            for (int i = 0; i < concurrentClients; i++) {
                final int clientId = i;
                final String deviceImei = "12345678901234" + clientId;
                
                executor.submit(() -> {
                    try {
                        GT06TestClient client = new GT06TestClient(TEST_HOST, TEST_PORT, deviceImei);
                        clients.add(client);
                        
                        // 连接并登录
                        boolean connected = client.connect(15000);
                        if (connected) {
                            boolean loggedIn = client.sendLogin();
                            if (loggedIn) {
                                // 发送一些测试消息
                                client.sendHeartbeat();
                                client.sendLocation(39.9042 + clientId * 0.001, 
                                                  116.4074 + clientId * 0.001, 
                                                  60 + clientId * 10);
                                
                                log.info("✅ Client {} completed successfully", clientId);
                            } else {
                                log.error("❌ Client {} failed to login", clientId);
                            }
                        } else {
                            log.error("❌ Client {} failed to connect", clientId);
                        }
                        
                    } catch (Exception e) {
                        log.error("❌ Client {} encountered error: {}", clientId, e.getMessage());
                    } finally {
                        latch.countDown();
                    }
                });
            }
            
            // 等待所有客户端完成
            boolean completed = latch.await(30, TimeUnit.SECONDS);
            Assertions.assertTrue(completed, "All concurrent clients should complete within timeout");
            
        } finally {
            // 清理资源
            executor.shutdown();
            for (GT06TestClient client : clients) {
                if (client.isConnected()) {
                    client.disconnect();
                }
            }
        }
        
        log.info("✅ Concurrent connections test passed");
    }
    
    @Test
    @Order(7)
    @DisplayName("测试消息序列号管理")
    void testSerialNumberManagement() {
        log.info("🧪 Testing serial number management...");
        
        // 连接并登录
        Assertions.assertTrue(testClient.connect(10000));
        Assertions.assertTrue(testClient.sendLogin());
        
        int initialSerial = testClient.getCurrentSerialNumber();
        
        // 发送多条消息
        testClient.sendHeartbeat();
        testClient.sendLocation(39.9042, 116.4074, 60);
        testClient.sendAlarm(AlarmMessage.AlarmType.VIBRATION);
        
        int finalSerial = testClient.getCurrentSerialNumber();
        
        // 验证序列号递增
        Assertions.assertTrue(finalSerial > initialSerial, 
            "Serial number should increment with each message");
        
        log.info("✅ Serial number management test passed");
    }
    
    @Test
    @Order(8)
    @DisplayName("测试连接重连机制")
    void testReconnectionMechanism() {
        log.info("🧪 Testing reconnection mechanism...");
        
        // 首次连接
        Assertions.assertTrue(testClient.connect(10000));
        Assertions.assertTrue(testClient.sendLogin());
        
        // 断开连接
        testClient.disconnect();
        Assertions.assertFalse(testClient.isConnected());
        
        // 等待一段时间
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 重新连接
        boolean reconnected = testClient.connect(10000);
        Assertions.assertTrue(reconnected, "Should be able to reconnect");
        
        boolean reloggedIn = testClient.sendLogin();
        Assertions.assertTrue(reloggedIn, "Should be able to login again");
        
        log.info("✅ Reconnection mechanism test passed");
    }
    
    @Test
    @Order(9)
    @DisplayName("测试长时间运行稳定性")
    void testLongRunningStability() {
        log.info("🧪 Testing long running stability...");
        
        // 启动设备模拟器
        Assertions.assertTrue(deviceSimulator.start());
        
        // 设置较短的间隔进行压力测试
        deviceSimulator.setHeartbeatInterval(5); // 5秒心跳
        deviceSimulator.setLocationReportInterval(10); // 10秒位置上报
        
        // 运行30秒
        try {
            Thread.sleep(30000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 检查运行状态
        Assertions.assertTrue(deviceSimulator.isRunning(), "Simulator should still be running");
        Assertions.assertTrue(deviceSimulator.isLoggedIn(), "Device should still be logged in");
        
        // 检查消息发送统计
        int totalMessages = deviceSimulator.getTotalMessagesSent();
        Assertions.assertTrue(totalMessages >= 5, 
            "Should have sent at least 5 messages in 30 seconds");
        
        log.info("📊 Long running test statistics:");
        deviceSimulator.printStatistics();
        
        log.info("✅ Long running stability test passed");
    }
}
