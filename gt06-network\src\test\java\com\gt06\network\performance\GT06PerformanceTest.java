package com.gt06.network.performance;

import com.gt06.network.client.DeviceSimulator;
import com.gt06.network.client.GT06TestClient;
import com.gt06.protocol.message.AlarmMessage;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * GT06服务器性能测试
 * 测试服务器在高并发和大量消息处理下的性能表现
 *
 * <AUTHOR> System
 * @version 1.0.0
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class GT06PerformanceTest {

    private static final String TEST_HOST = "localhost";
    private static final int TEST_PORT = 8888;

    @Test
    @Order(1)
    @DisplayName("测试高并发连接性能")
    void testHighConcurrencyConnections() throws InterruptedException {
        log.info("🚀 Starting high concurrency connection test...");

        int concurrentConnections = 50;
        ExecutorService executor = Executors.newFixedThreadPool(concurrentConnections);
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch completeLatch = new CountDownLatch(concurrentConnections);

        AtomicInteger successfulConnections = new AtomicInteger(0);
        AtomicInteger failedConnections = new AtomicInteger(0);
        AtomicLong totalConnectionTime = new AtomicLong(0);

        List<GT06TestClient> clients = new ArrayList<>();

        try {
            // 创建并发连接任务
            for (int i = 0; i < concurrentConnections; i++) {
                final int clientId = i;
                final String deviceImei = String.format("PERF%015d", clientId);

                executor.submit(() -> {
                    GT06TestClient client = null;
                    try {
                        // 等待统一开始信号
                        startLatch.await();

                        long startTime = System.currentTimeMillis();

                        client = new GT06TestClient(TEST_HOST, TEST_PORT, deviceImei);
                        synchronized (clients) {
                            clients.add(client);
                        }

                        // 连接并登录
                        boolean connected = client.connect(30000);
                        if (connected && client.sendLogin()) {
                            long connectionTime = System.currentTimeMillis() - startTime;
                            totalConnectionTime.addAndGet(connectionTime);
                            successfulConnections.incrementAndGet();

                            log.debug("✅ Client {} connected successfully in {}ms",
                                clientId, connectionTime);
                        } else {
                            failedConnections.incrementAndGet();
                            log.warn("❌ Client {} failed to connect or login", clientId);
                        }

                    } catch (Exception e) {
                        failedConnections.incrementAndGet();
                        log.error("❌ Client {} encountered error: {}", clientId, e.getMessage());
                    } finally {
                        completeLatch.countDown();
                    }
                });
            }

            // 开始测试
            long testStartTime = System.currentTimeMillis();
            startLatch.countDown();

            // 等待所有连接完成
            boolean completed = completeLatch.await(120, TimeUnit.SECONDS);
            long testDuration = System.currentTimeMillis() - testStartTime;

            // 输出结果
            log.info("📊 High Concurrency Connection Test Results:");
            log.info("  - Total clients: {}", concurrentConnections);
            log.info("  - Successful connections: {}", successfulConnections.get());
            log.info("  - Failed connections: {}", failedConnections.get());
            log.info("  - Success rate: {:.2f}%",
                (double) successfulConnections.get() / concurrentConnections * 100);
            log.info("  - Total test duration: {}ms", testDuration);

            if (successfulConnections.get() > 0) {
                log.info("  - Average connection time: {}ms",
                    totalConnectionTime.get() / successfulConnections.get());
            }

            // 验证结果
            Assertions.assertTrue(completed, "All connections should complete within timeout");
            Assertions.assertTrue(successfulConnections.get() >= concurrentConnections * 0.8,
                "At least 80% of connections should succeed");

        } finally {
            // 清理资源
            executor.shutdown();
            for (GT06TestClient client : clients) {
                if (client != null && client.isConnected()) {
                    client.disconnect();
                }
            }
        }

        log.info("✅ High concurrency connection test completed");
    }

    @Test
    @Order(2)
    @DisplayName("测试消息吞吐量性能")
    void testMessageThroughputPerformance() {
        log.info("🚀 Starting message throughput performance test...");

        int numberOfClients = 20;
        int messagesPerClient = 100;
        int totalExpectedMessages = numberOfClients * messagesPerClient;

        ExecutorService executor = Executors.newFixedThreadPool(numberOfClients);
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch completeLatch = new CountDownLatch(numberOfClients);

        AtomicInteger totalMessagesSent = new AtomicInteger(0);
        AtomicInteger totalMessagesSucceeded = new AtomicInteger(0);
        AtomicLong totalMessageTime = new AtomicLong(0);

        List<GT06TestClient> clients = new ArrayList<>();

        try {
            // 创建客户端并连接
            for (int i = 0; i < numberOfClients; i++) {
                final int clientId = i;
                final String deviceImei = String.format("THRU%015d", clientId);

                executor.submit(() -> {
                    GT06TestClient client = null;
                    try {
                        client = new GT06TestClient(TEST_HOST, TEST_PORT, deviceImei);
                        synchronized (clients) {
                            clients.add(client);
                        }

                        // 连接并登录
                        if (!client.connect(30000) || !client.sendLogin()) {
                            log.error("❌ Client {} failed to connect or login", clientId);
                            return;
                        }

                        // 等待统一开始信号
                        startLatch.await();

                        // 发送消息
                        for (int msgId = 0; msgId < messagesPerClient; msgId++) {
                            long msgStartTime = System.currentTimeMillis();

                            boolean success = false;
                            switch (msgId % 4) {
                                case 0:
                                    success = client.sendHeartbeat();
                                    break;
                                case 1:
                                    success = client.sendLocation(
                                        39.9042 + clientId * 0.001,
                                        116.4074 + clientId * 0.001,
                                        60 + msgId % 80);
                                    break;
                                case 2:
                                    success = client.sendAlarm(AlarmMessage.AlarmType.VIBRATION);
                                    break;
                                case 3:
                                    success = client.sendLocation(
                                        39.9042 + clientId * 0.001,
                                        116.4074 + clientId * 0.001,
                                        60 + msgId % 80);
                                    break;
                            }

                            totalMessagesSent.incrementAndGet();
                            if (success) {
                                totalMessagesSucceeded.incrementAndGet();
                                long msgTime = System.currentTimeMillis() - msgStartTime;
                                totalMessageTime.addAndGet(msgTime);
                            }

                            // 短暂延迟避免过于密集
                            Thread.sleep(10);
                        }

                        log.debug("✅ Client {} completed sending {} messages",
                            clientId, messagesPerClient);

                    } catch (Exception e) {
                        log.error("❌ Client {} encountered error: {}", clientId, e.getMessage());
                    } finally {
                        completeLatch.countDown();
                    }
                });
            }

            // 等待所有客户端连接完成
            Thread.sleep(5000);

            // 开始消息发送测试
            long testStartTime = System.currentTimeMillis();
            startLatch.countDown();

            // 等待所有消息发送完成
            boolean completed = completeLatch.await(300, TimeUnit.SECONDS);
            long testDuration = System.currentTimeMillis() - testStartTime;

            // 输出结果
            log.info("📊 Message Throughput Performance Test Results:");
            log.info("  - Total clients: {}", numberOfClients);
            log.info("  - Messages per client: {}", messagesPerClient);
            log.info("  - Total expected messages: {}", totalExpectedMessages);
            log.info("  - Total messages sent: {}", totalMessagesSent.get());
            log.info("  - Total messages succeeded: {}", totalMessagesSucceeded.get());
            log.info("  - Success rate: {:.2f}%",
                (double) totalMessagesSucceeded.get() / totalMessagesSent.get() * 100);
            log.info("  - Test duration: {}ms", testDuration);
            log.info("  - Throughput: {:.2f} messages/second",
                (double) totalMessagesSucceeded.get() / testDuration * 1000);

            if (totalMessagesSucceeded.get() > 0) {
                log.info("  - Average message processing time: {}ms",
                    totalMessageTime.get() / totalMessagesSucceeded.get());
            }

            // 验证结果
            Assertions.assertTrue(completed, "All message sending should complete within timeout");
            Assertions.assertTrue(totalMessagesSucceeded.get() >= totalExpectedMessages * 0.9,
                "At least 90% of messages should succeed");

        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } finally {
            // 清理资源
            executor.shutdown();
            for (GT06TestClient client : clients) {
                if (client != null && client.isConnected()) {
                    client.disconnect();
                }
            }
        }

        log.info("✅ Message throughput performance test completed");
    }

    @Test
    @Order(3)
    @DisplayName("测试长时间运行稳定性")
    void testLongRunningStability() {
        log.info("🚀 Starting long running stability test...");

        int numberOfSimulators = 10;
        int testDurationMinutes = 5; // 5分钟测试

        List<DeviceSimulator> simulators = new ArrayList<>();

        try {
            // 创建并启动设备模拟器
            for (int i = 0; i < numberOfSimulators; i++) {
                String deviceId = String.format("STAB%015d", i);
                DeviceSimulator simulator = new DeviceSimulator(TEST_HOST, TEST_PORT, deviceId);

                // 设置较短的间隔增加消息密度
                simulator.setHeartbeatInterval(15); // 15秒心跳
                simulator.setLocationReportInterval(30); // 30秒位置上报

                boolean started = simulator.start();
                if (started) {
                    simulators.add(simulator);
                    log.info("✅ Simulator {} started successfully", i);
                } else {
                    log.error("❌ Failed to start simulator {}", i);
                }

                // 短暂延迟避免同时启动造成冲击
                Thread.sleep(500);
            }

            log.info("🏃 Running stability test for {} minutes with {} simulators...",
                testDurationMinutes, simulators.size());

            // 运行指定时间
            long testStartTime = System.currentTimeMillis();
            Thread.sleep(testDurationMinutes * 60 * 1000);
            long testDuration = System.currentTimeMillis() - testStartTime;

            // 收集统计信息
            int totalRunningSimulators = 0;
            int totalLoggedInSimulators = 0;
            int totalMessagesSent = 0;

            for (int i = 0; i < simulators.size(); i++) {
                DeviceSimulator simulator = simulators.get(i);

                if (simulator.isRunning()) {
                    totalRunningSimulators++;
                }
                if (simulator.isLoggedIn()) {
                    totalLoggedInSimulators++;
                }
                totalMessagesSent += simulator.getTotalMessagesSent();

                log.info("📊 Simulator {} statistics:", i);
                simulator.printStatistics();
            }

            // 输出总体结果
            log.info("📊 Long Running Stability Test Results:");
            log.info("  - Test duration: {} minutes", testDurationMinutes);
            log.info("  - Total simulators: {}", numberOfSimulators);
            log.info("  - Running simulators: {}", totalRunningSimulators);
            log.info("  - Logged in simulators: {}", totalLoggedInSimulators);
            log.info("  - Total messages sent: {}", totalMessagesSent);
            log.info("  - Average messages per simulator: {}",
                simulators.size() > 0 ? totalMessagesSent / simulators.size() : 0);
            log.info("  - Messages per minute: {:.2f}",
                (double) totalMessagesSent / testDurationMinutes);

            // 验证结果
            Assertions.assertTrue(totalRunningSimulators >= numberOfSimulators * 0.8,
                "At least 80% of simulators should still be running");
            Assertions.assertTrue(totalLoggedInSimulators >= numberOfSimulators * 0.8,
                "At least 80% of simulators should still be logged in");
            Assertions.assertTrue(totalMessagesSent > 0,
                "Should have sent some messages during the test");

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("❌ Test was interrupted", e);
        } finally {
            // 停止所有模拟器
            for (DeviceSimulator simulator : simulators) {
                if (simulator.isRunning()) {
                    simulator.stop();
                }
            }
        }

        log.info("✅ Long running stability test completed");
    }

    @Test
    @Order(4)
    @DisplayName("测试内存使用情况")
    void testMemoryUsage() throws InterruptedException {
        log.info("🚀 Starting memory usage test...");

        Runtime runtime = Runtime.getRuntime();

        // 记录初始内存使用
        System.gc(); // 强制垃圾回收
        long initialMemory = runtime.totalMemory() - runtime.freeMemory();

        log.info("📊 Initial memory usage: {} MB", initialMemory / 1024 / 1024);

        int numberOfClients = 100;
        List<GT06TestClient> clients = new ArrayList<>();

        long peakMemory = 0;
        try {
            // 创建大量客户端连接
            for (int i = 0; i < numberOfClients; i++) {
                String deviceImei = String.format("MEM%016d", i);
                GT06TestClient client = new GT06TestClient(TEST_HOST, TEST_PORT, deviceImei);

                if (client.connect(10000) && client.sendLogin()) {
                    clients.add(client);

                    // 发送一些消息
                    client.sendHeartbeat();
                    client.sendLocation(39.9042 + i * 0.001, 116.4074 + i * 0.001, 60);
                }

                // 每10个连接检查一次内存
                if ((i + 1) % 10 == 0) {
                    long currentMemory = runtime.totalMemory() - runtime.freeMemory();
                    log.debug("Memory after {} clients: {} MB",
                            i + 1, currentMemory / 1024 / 1024);
                }
            }

            // 记录峰值内存使用
            peakMemory = runtime.totalMemory() - runtime.freeMemory();
            log.info("📊 Peak memory usage: {} MB", peakMemory / 1024 / 1024);
            log.info("📊 Memory increase: {} MB", (peakMemory - initialMemory) / 1024 / 1024);
            log.info("📊 Average memory per client: {} KB",
                    (peakMemory - initialMemory) / clients.size() / 1024);

            // 验证内存使用合理性
            long memoryIncrease = peakMemory - initialMemory;
            long averageMemoryPerClient = memoryIncrease / clients.size();

            Assertions.assertTrue(averageMemoryPerClient < 1024 * 1024, // 1MB per client
                    "Memory usage per client should be reasonable");

        } finally {
            // 清理所有连接
            for (GT06TestClient client : clients) {
                if (client.isConnected()) {
                    client.disconnect();
                }
            }

            // 强制垃圾回收并检查内存释放
            System.gc();
            Thread.sleep(2000);

            long finalMemory = runtime.totalMemory() - runtime.freeMemory();
            log.info("📊 Final memory usage: {} MB", finalMemory / 1024 / 1024);
            log.info("📊 Memory released: {} MB", (peakMemory - finalMemory) / 1024 / 1024);
        }

        log.info("✅ Memory usage test completed");
    }
}
