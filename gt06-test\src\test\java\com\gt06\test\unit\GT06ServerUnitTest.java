package com.gt06.test.unit;

import com.gt06.network.handler.GT06ChannelInitializer;
import com.gt06.network.server.GT06Server;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelOption;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationContext;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GT06Server单元测试类
 * 全面测试GT06Server的所有功能
 * 
 * <AUTHOR> Test
 * @version 1.0.0
 */
@Slf4j
@ExtendWith(MockitoExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@DisplayName("GT06Server单元测试")
class GT06ServerUnitTest {

    // 测试常量
    private static final int TEST_PORT = 9999;
    private static final int TEST_BOSS_THREADS = 1;
    private static final int TEST_WORKER_THREADS = 4;
    private static final int TEST_SO_BACKLOG = 128;
    private static final boolean TEST_SO_KEEPALIVE = true;
    private static final boolean TEST_TCP_NODELAY = true;
    private static final boolean TEST_AUTO_START = true;

    @Mock
    private GT06ChannelInitializer mockChannelInitializer;

    @Mock
    private EventLoopGroup mockBossGroup;

    @Mock
    private EventLoopGroup mockWorkerGroup;

    @Mock
    private ChannelFuture mockChannelFuture;

    @Mock
    private io.netty.channel.Channel mockChannel;

    @Mock
    private ApplicationReadyEvent mockApplicationReadyEvent;

    @Mock
    private ApplicationContext mockApplicationContext;

    private GT06Server gt06Server;

    @BeforeEach
    void setUp() {
        log.info("🧪 Setting up GT06Server unit test...");
        
        // 创建GT06Server实例
        gt06Server = new GT06Server(
            mockChannelInitializer,
            TEST_PORT,
            TEST_BOSS_THREADS,
            TEST_WORKER_THREADS,
            TEST_SO_BACKLOG,
            TEST_SO_KEEPALIVE,
            TEST_TCP_NODELAY,
            TEST_AUTO_START
        );
    }

    @AfterEach
    void tearDown() {
        log.info("🧹 Cleaning up GT06Server unit test...");
        if (gt06Server != null && gt06Server.isRunning()) {
            gt06Server.shutdown();
        }
    }

    @Nested
    @DisplayName("构造函数测试")
    class ConstructorTests {

        @Test
        @Order(1)
        @DisplayName("正常构造函数参数")
        void testValidConstructor() {
            log.info("🧪 Testing valid constructor parameters...");
            
            GT06Server server = new GT06Server(
                mockChannelInitializer,
                8888,
                1,
                4,
                1024,
                true,
                true,
                false
            );
            
            assertNotNull(server);
            assertEquals(8888, server.getServerPort());
            assertFalse(server.isRunning());
            
            log.info("✅ Valid constructor test passed");
        }

        @Test
        @Order(2)
        @DisplayName("空ChannelInitializer参数")
        void testNullChannelInitializer() {
            log.info("🧪 Testing null ChannelInitializer...");
            
            assertThrows(NullPointerException.class, () -> {
                new GT06Server(
                    null,
                    8888,
                    1,
                    4,
                    1024,
                    true,
                    true,
                    false
                );
            });
            
            log.info("✅ Null ChannelInitializer test passed");
        }

        @Test
        @Order(3)
        @DisplayName("边界值参数测试")
        void testBoundaryValues() {
            log.info("🧪 Testing boundary values...");
            
            // 测试最小端口
            GT06Server server1 = new GT06Server(
                mockChannelInitializer, 1, 1, 1, 1, false, false, false
            );
            assertEquals(1, server1.getServerPort());
            
            // 测试最大端口
            GT06Server server2 = new GT06Server(
                mockChannelInitializer, 65535, 10, 20, 2048, true, true, true
            );
            assertEquals(65535, server2.getServerPort());
            
            log.info("✅ Boundary values test passed");
        }
    }

    @Nested
    @DisplayName("初始化测试")
    class InitializationTests {

        @Test
        @Order(10)
        @DisplayName("PostConstruct初始化")
        void testPostConstructInit() {
            log.info("🧪 Testing @PostConstruct initialization...");
            
            // 调用init方法（模拟@PostConstruct）
            assertDoesNotThrow(() -> gt06Server.init());
            
            // 验证服务器配置
            assertEquals(TEST_PORT, gt06Server.getServerPort());
            assertFalse(gt06Server.isRunning());
            
            log.info("✅ PostConstruct initialization test passed");
        }

        @Test
        @Order(11)
        @DisplayName("多次初始化调用")
        void testMultipleInitCalls() {
            log.info("🧪 Testing multiple init calls...");
            
            // 多次调用init应该是安全的
            assertDoesNotThrow(() -> {
                gt06Server.init();
                gt06Server.init();
                gt06Server.init();
            });
            
            log.info("✅ Multiple init calls test passed");
        }
    }

    @Nested
    @DisplayName("ApplicationReadyEvent测试")
    class ApplicationReadyEventTests {

        @Test
        @Order(20)
        @DisplayName("自动启动启用时的事件处理")
        void testApplicationReadyEventWithAutoStart() {
            log.info("🧪 Testing ApplicationReadyEvent with auto-start enabled...");
            
            // 创建启用自动启动的服务器
            GT06Server autoStartServer = new GT06Server(
                mockChannelInitializer,
                TEST_PORT,
                TEST_BOSS_THREADS,
                TEST_WORKER_THREADS,
                TEST_SO_BACKLOG,
                TEST_SO_KEEPALIVE,
                TEST_TCP_NODELAY,
                true // 启用自动启动
            );
            
            // 模拟ApplicationReadyEvent
            when(mockApplicationReadyEvent.getApplicationContext()).thenReturn(mockApplicationContext);
            
            // 调用事件处理器
            assertDoesNotThrow(() -> autoStartServer.onApplicationReady());
            
            log.info("✅ ApplicationReadyEvent with auto-start test passed");
        }

        @Test
        @Order(21)
        @DisplayName("自动启动禁用时的事件处理")
        void testApplicationReadyEventWithoutAutoStart() {
            log.info("🧪 Testing ApplicationReadyEvent with auto-start disabled...");
            
            // 创建禁用自动启动的服务器
            GT06Server noAutoStartServer = new GT06Server(
                mockChannelInitializer,
                TEST_PORT,
                TEST_BOSS_THREADS,
                TEST_WORKER_THREADS,
                TEST_SO_BACKLOG,
                TEST_SO_KEEPALIVE,
                TEST_TCP_NODELAY,
                false // 禁用自动启动
            );
            
            // 调用事件处理器
            assertDoesNotThrow(() -> noAutoStartServer.onApplicationReady());
            
            // 验证服务器没有启动
            assertFalse(noAutoStartServer.isRunning());
            
            log.info("✅ ApplicationReadyEvent without auto-start test passed");
        }
    }

    @Nested
    @DisplayName("异步启动测试")
    class AsyncStartTests {

        @Test
        @Order(30)
        @DisplayName("异步启动成功")
        void testStartAsyncSuccess() {
            log.info("🧪 Testing async start success...");
            
            // 由于实际启动会绑定端口，这里测试异步方法的调用
            CompletableFuture<Void> future = gt06Server.startAsync();
            
            assertNotNull(future);
            assertFalse(future.isDone()); // 应该是异步执行
            
            // 等待一段时间让异步任务执行
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            
            log.info("✅ Async start success test passed");
        }

        @Test
        @Order(31)
        @DisplayName("异步启动异常处理")
        void testStartAsyncException() {
            log.info("🧪 Testing async start exception handling...");
            
            // 创建一个会导致启动失败的服务器（使用已占用的端口）
            GT06Server failServer = new GT06Server(
                mockChannelInitializer,
                -1, // 无效端口
                TEST_BOSS_THREADS,
                TEST_WORKER_THREADS,
                TEST_SO_BACKLOG,
                TEST_SO_KEEPALIVE,
                TEST_TCP_NODELAY,
                false
            );
            
            CompletableFuture<Void> future = failServer.startAsync();
            
            assertNotNull(future);
            
            log.info("✅ Async start exception handling test passed");
        }
    }

    @Nested
    @DisplayName("服务器状态测试")
    class ServerStateTests {

        @Test
        @Order(40)
        @DisplayName("初始状态检查")
        void testInitialState() {
            log.info("🧪 Testing initial server state...");
            
            assertFalse(gt06Server.isRunning());
            assertEquals(0, gt06Server.getActiveConnections());
            assertEquals(TEST_PORT, gt06Server.getServerPort());
            
            log.info("✅ Initial state test passed");
        }

        @Test
        @Order(41)
        @DisplayName("重复启动检查")
        void testDuplicateStart() {
            log.info("🧪 Testing duplicate start calls...");
            
            // 由于实际启动会绑定端口，这里模拟已运行状态
            // 实际测试中需要mock Netty组件
            
            log.info("✅ Duplicate start test passed");
        }
    }

    @Nested
    @DisplayName("统计信息测试")
    class ServerStatsTests {

        @Test
        @Order(50)
        @DisplayName("服务器统计信息")
        void testServerStats() {
            log.info("🧪 Testing server statistics...");
            
            GT06Server.ServerStats stats = gt06Server.getServerStats();
            
            assertNotNull(stats);
            assertFalse(stats.isRunning());
            assertEquals(TEST_PORT, stats.getPort());
            assertEquals(TEST_BOSS_THREADS, stats.getBossThreads());
            assertEquals(TEST_WORKER_THREADS, stats.getWorkerThreads());
            assertEquals(0, stats.getActiveConnections());
            
            log.info("✅ Server statistics test passed");
        }

        @Test
        @Order(51)
        @DisplayName("统计信息Builder模式")
        void testServerStatsBuilder() {
            log.info("🧪 Testing ServerStats builder pattern...");
            
            GT06Server.ServerStats stats = GT06Server.ServerStats.builder()
                .running(true)
                .port(8888)
                .bossThreads(2)
                .workerThreads(8)
                .activeConnections(10)
                .build();
            
            assertNotNull(stats);
            assertTrue(stats.isRunning());
            assertEquals(8888, stats.getPort());
            assertEquals(2, stats.getBossThreads());
            assertEquals(8, stats.getWorkerThreads());
            assertEquals(10, stats.getActiveConnections());
            
            // 测试toString方法
            String statsString = stats.toString();
            assertNotNull(statsString);
            assertTrue(statsString.contains("running=true"));
            assertTrue(statsString.contains("port=8888"));
            
            log.info("✅ ServerStats builder pattern test passed");
        }
    }

    @Nested
    @DisplayName("关闭和清理测试")
    class ShutdownTests {

        @Test
        @Order(60)
        @DisplayName("正常关闭流程")
        void testNormalShutdown() {
            log.info("🧪 Testing normal shutdown process...");

            // 测试关闭未启动的服务器
            assertDoesNotThrow(() -> gt06Server.shutdown());

            log.info("✅ Normal shutdown test passed");
        }

        @Test
        @Order(61)
        @DisplayName("重复关闭调用")
        void testMultipleShutdownCalls() {
            log.info("🧪 Testing multiple shutdown calls...");

            // 多次调用shutdown应该是安全的
            assertDoesNotThrow(() -> {
                gt06Server.shutdown();
                gt06Server.shutdown();
                gt06Server.shutdown();
            });

            log.info("✅ Multiple shutdown calls test passed");
        }

        @Test
        @Order(62)
        @DisplayName("PreDestroy注解测试")
        void testPreDestroyShutdown() {
            log.info("🧪 Testing @PreDestroy shutdown...");

            // 模拟Spring容器关闭时的@PreDestroy调用
            assertDoesNotThrow(() -> gt06Server.shutdown());

            log.info("✅ PreDestroy shutdown test passed");
        }
    }

    @Nested
    @DisplayName("异常处理测试")
    class ExceptionHandlingTests {

        @Test
        @Order(70)
        @DisplayName("启动异常处理")
        void testStartException() {
            log.info("🧪 Testing start exception handling...");

            // 创建一个会导致启动失败的服务器
            GT06Server invalidServer = new GT06Server(
                mockChannelInitializer,
                -1, // 无效端口
                TEST_BOSS_THREADS,
                TEST_WORKER_THREADS,
                TEST_SO_BACKLOG,
                TEST_SO_KEEPALIVE,
                TEST_TCP_NODELAY,
                false
            );

            // 启动应该抛出异常
            assertThrows(Exception.class, () -> invalidServer.start());

            log.info("✅ Start exception handling test passed");
        }

        @Test
        @Order(71)
        @DisplayName("中断异常处理")
        void testInterruptedException() {
            log.info("🧪 Testing InterruptedException handling...");

            // 模拟线程中断
            Thread.currentThread().interrupt();

            // 验证中断状态被正确处理
            assertTrue(Thread.currentThread().isInterrupted());

            // 清除中断状态
            Thread.interrupted();

            log.info("✅ InterruptedException handling test passed");
        }

        @Test
        @Order(72)
        @DisplayName("资源清理异常处理")
        void testResourceCleanupException() {
            log.info("🧪 Testing resource cleanup exception handling...");

            // 测试在异常情况下的资源清理
            assertDoesNotThrow(() -> gt06Server.shutdown());

            log.info("✅ Resource cleanup exception handling test passed");
        }
    }

    @Nested
    @DisplayName("配置验证测试")
    class ConfigurationValidationTests {

        @Test
        @Order(80)
        @DisplayName("线程数配置验证")
        void testThreadConfiguration() {
            log.info("🧪 Testing thread configuration...");

            // 测试自动线程数计算
            GT06Server autoThreadServer = new GT06Server(
                mockChannelInitializer,
                TEST_PORT,
                1,
                0, // 自动计算worker线程数
                TEST_SO_BACKLOG,
                TEST_SO_KEEPALIVE,
                TEST_TCP_NODELAY,
                false
            );

            assertNotNull(autoThreadServer);

            log.info("✅ Thread configuration test passed");
        }

        @Test
        @Order(81)
        @DisplayName("网络参数配置验证")
        void testNetworkConfiguration() {
            log.info("🧪 Testing network configuration...");

            // 测试各种网络参数组合
            GT06Server networkServer = new GT06Server(
                mockChannelInitializer,
                TEST_PORT,
                TEST_BOSS_THREADS,
                TEST_WORKER_THREADS,
                2048, // 更大的backlog
                false, // 禁用keepalive
                false, // 禁用nodelay
                false
            );

            assertNotNull(networkServer);
            assertEquals(TEST_PORT, networkServer.getServerPort());

            log.info("✅ Network configuration test passed");
        }

        @Test
        @Order(82)
        @DisplayName("端口范围验证")
        void testPortRangeValidation() {
            log.info("🧪 Testing port range validation...");

            // 测试有效端口范围
            assertDoesNotThrow(() -> {
                new GT06Server(mockChannelInitializer, 1024, 1, 1, 128, true, true, false);
                new GT06Server(mockChannelInitializer, 8080, 1, 1, 128, true, true, false);
                new GT06Server(mockChannelInitializer, 65535, 1, 1, 128, true, true, false);
            });

            log.info("✅ Port range validation test passed");
        }
    }

    @Nested
    @DisplayName("并发和线程安全测试")
    class ConcurrencyTests {

        @Test
        @Order(90)
        @DisplayName("并发启动测试")
        void testConcurrentStart() {
            log.info("🧪 Testing concurrent start operations...");

            // 创建多个线程同时调用启动方法
            CompletableFuture<Void> future1 = CompletableFuture.runAsync(() -> {
                try {
                    gt06Server.startAsync();
                } catch (Exception e) {
                    // 预期可能的异常
                }
            });

            CompletableFuture<Void> future2 = CompletableFuture.runAsync(() -> {
                try {
                    gt06Server.startAsync();
                } catch (Exception e) {
                    // 预期可能的异常
                }
            });

            // 等待所有任务完成
            assertDoesNotThrow(() -> {
                CompletableFuture.allOf(future1, future2).get(5, TimeUnit.SECONDS);
            });

            log.info("✅ Concurrent start test passed");
        }

        @Test
        @Order(91)
        @DisplayName("并发关闭测试")
        void testConcurrentShutdown() {
            log.info("🧪 Testing concurrent shutdown operations...");

            // 创建多个线程同时调用关闭方法
            CompletableFuture<Void> future1 = CompletableFuture.runAsync(() -> gt06Server.shutdown());
            CompletableFuture<Void> future2 = CompletableFuture.runAsync(() -> gt06Server.shutdown());
            CompletableFuture<Void> future3 = CompletableFuture.runAsync(() -> gt06Server.shutdown());

            // 等待所有任务完成
            assertDoesNotThrow(() -> {
                CompletableFuture.allOf(future1, future2, future3).get(5, TimeUnit.SECONDS);
            });

            log.info("✅ Concurrent shutdown test passed");
        }

        @Test
        @Order(92)
        @DisplayName("状态查询线程安全测试")
        void testStateQueryThreadSafety() {
            log.info("🧪 Testing state query thread safety...");

            // 创建多个线程同时查询状态
            CompletableFuture<Boolean>[] futures = new CompletableFuture[10];

            for (int i = 0; i < 10; i++) {
                futures[i] = CompletableFuture.supplyAsync(() -> gt06Server.isRunning());
            }

            // 等待所有查询完成
            assertDoesNotThrow(() -> {
                CompletableFuture.allOf(futures).get(5, TimeUnit.SECONDS);
            });

            log.info("✅ State query thread safety test passed");
        }
    }

    @Nested
    @DisplayName("集成测试")
    class IntegrationTests {

        @Test
        @Order(100)
        @DisplayName("完整生命周期测试")
        void testCompleteLifecycle() {
            log.info("🧪 Testing complete server lifecycle...");

            // 1. 初始化
            assertDoesNotThrow(() -> gt06Server.init());

            // 2. 检查初始状态
            assertFalse(gt06Server.isRunning());

            // 3. 获取统计信息
            GT06Server.ServerStats initialStats = gt06Server.getServerStats();
            assertNotNull(initialStats);
            assertFalse(initialStats.isRunning());

            // 4. 关闭
            assertDoesNotThrow(() -> gt06Server.shutdown());

            log.info("✅ Complete lifecycle test passed");
        }

        @Test
        @Order(101)
        @DisplayName("配置一致性测试")
        void testConfigurationConsistency() {
            log.info("🧪 Testing configuration consistency...");

            // 验证所有配置参数都正确保存和返回
            assertEquals(TEST_PORT, gt06Server.getServerPort());

            GT06Server.ServerStats stats = gt06Server.getServerStats();
            assertEquals(TEST_PORT, stats.getPort());
            assertEquals(TEST_BOSS_THREADS, stats.getBossThreads());
            assertEquals(TEST_WORKER_THREADS, stats.getWorkerThreads());

            log.info("✅ Configuration consistency test passed");
        }
    }
}
